# 晨羽智云 Go SDK API 测试文档

本文档提供了晨羽智云 Go SDK 所有 API 接口的详细测试用例和测试方法。

## 目录

1. [测试环境准备](#测试环境准备)
2. [财务相关API测试](#财务相关api测试)
3. [应用管理API测试](#应用管理api测试)
4. [实例操作API测试](#实例操作api测试)
5. [资源查询API测试](#资源查询api测试)
6. [错误处理测试](#错误处理测试)
7. [完整测试套件](#完整测试套件)

## 测试环境准备

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置测试环境

```go
package main

import (
    "github.com/chenyu-ai/sdk-go/client"
    "github.com/chenyu-ai/sdk-go/types"
)

// 测试配置
const (
    TestBaseURL = "https://api.chenyu.cn/api/v1"  // 测试环境URL
    TestToken   = "your-test-token-here"          // 测试Token
)

// 创建测试客户端
func createTestClient() *client.Client {
    return client.NewClient(TestBaseURL, TestToken)
}
```

## 财务相关API测试

### 1. GetBalance() - 查询余额测试

**测试目标**: 验证余额查询功能的正确性

**测试用例**:

```go
func TestGetBalance(t *testing.T) {
    client := createTestClient()
    
    // 测试用例1: 正常查询余额
    t.Run("正常查询余额", func(t *testing.T) {
        req := &types.BalanceRequest{}
        resp, err := client.GetBalance(req)
        
        // 验证无错误
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        
        // 验证响应结构
        assert.NotNil(t, resp.Balance)
        assert.NotNil(t, resp.Cards)
        
        // 验证算力卡信息
        for _, card := range resp.Cards {
            assert.NotEmpty(t, card.Uuid)
            assert.NotEmpty(t, card.CardNo)
            assert.NotEmpty(t, card.StatusTxt)
            assert.True(t, card.LeaveAmount.GreaterThanOrEqual(decimal.Zero))
        }
        
        fmt.Printf("账户余额: %s\n", resp.Balance.String())
        fmt.Printf("算力卡数量: %d\n", len(resp.Cards))
    })
}
```

**预期结果**:
- 返回账户余额信息
- 返回算力卡列表
- 所有金额字段为非负数
- 必填字段不为空

### 2. GetRecharge() - 查询充值记录测试

**测试目标**: 验证充值记录查询功能

**测试用例**:

```go
func TestGetRecharge(t *testing.T) {
    client := createTestClient()
    
    // 测试用例1: 分页查询充值记录
    t.Run("分页查询充值记录", func(t *testing.T) {
        req := &types.RechargeListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 10,
            },
        }
        resp, err := client.GetRecharge(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.GreaterOrEqual(t, resp.Total, 0)
        assert.LessOrEqual(t, len(resp.Items), 10)
        
        // 验证充值记录字段
        for _, item := range resp.Items {
            assert.NotEmpty(t, item.OutTradeNo)
            assert.NotEmpty(t, item.Gateway)
            assert.True(t, item.Amount.GreaterThan(decimal.Zero))
        }
    })
    
    // 测试用例2: 关键词搜索
    t.Run("关键词搜索充值记录", func(t *testing.T) {
        req := &types.RechargeListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 5,
            },
            KW: "支付宝",
        }
        resp, err := client.GetRecharge(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
    })
}
```

### 3. GetBill() - 查询账单记录测试

**测试目标**: 验证账单查询功能

**测试用例**:

```go
func TestGetBill(t *testing.T) {
    client := createTestClient()
    
    // 测试用例1: 查询账单列表
    t.Run("查询账单列表", func(t *testing.T) {
        req := &types.BillListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 20,
            },
        }
        resp, err := client.GetBill(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.GreaterOrEqual(t, resp.Total, 0)
        
        // 验证账单记录字段
        for _, bill := range resp.Items {
            assert.NotEmpty(t, bill.OrderNo)
            assert.NotEmpty(t, bill.Show)
            assert.NotNil(t, bill.OccurredAmount)
        }
        
        fmt.Printf("账单总数: %d\n", resp.Total)
    })
    
    // 测试用例2: 搜索特定账单
    t.Run("搜索特定账单", func(t *testing.T) {
        req := &types.BillListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 10,
            },
            KW: "实例",
        }
        resp, err := client.GetBill(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
    })
}
```

## 应用管理API测试

### 4. GetPods() - 查询镜像列表测试

**测试目标**: 验证镜像列表查询功能

**测试用例**:

```go
func TestGetPods(t *testing.T) {
    client := createTestClient()
    
    // 测试用例1: 查询所有镜像
    t.Run("查询所有镜像", func(t *testing.T) {
        req := &types.PodListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 10,
            },
        }
        resp, err := client.GetPods(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.GreaterOrEqual(t, resp.Total, 0)
        
        // 验证镜像信息
        for _, pod := range resp.Items {
            assert.NotEmpty(t, pod.Uuid)
            assert.NotEmpty(t, pod.Title)
            assert.NotEmpty(t, pod.StatusTxt)
        }
        
        fmt.Printf("镜像总数: %d\n", resp.Total)
    })
    
    // 测试用例2: 关键词搜索镜像
    t.Run("搜索Jupyter镜像", func(t *testing.T) {
        req := &types.PodListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 5,
            },
            Kw: "jupyter",
        }
        resp, err := client.GetPods(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        
        // 验证搜索结果包含关键词
        for _, pod := range resp.Items {
            assert.Contains(t, strings.ToLower(pod.Title), "jupyter")
        }
    })
    
    // 测试用例3: 测试分页参数默认值
    t.Run("测试分页默认值", func(t *testing.T) {
        req := &types.PodListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     0,  // 应该被设置为1
                PageSize: 0,  // 应该被设置为10
            },
        }
        resp, err := client.GetPods(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.LessOrEqual(t, len(resp.Items), 10)
    })
}
```

### 5. GetInstances() - 查询实例列表测试

**测试目标**: 验证实例列表查询功能

**测试用例**:

```go
func TestGetInstances(t *testing.T) {
    client := createTestClient()
    
    // 测试用例1: 查询所有实例
    t.Run("查询所有实例", func(t *testing.T) {
        req := &types.InstanceListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 10,
            },
        }
        resp, err := client.GetInstances(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.GreaterOrEqual(t, resp.Total, 0)
        
        // 验证实例信息
        for _, instance := range resp.Instance {
            assert.NotEmpty(t, instance.Uuid)
            assert.NotEmpty(t, instance.Title)
            assert.NotEmpty(t, instance.StatusTxt)
            assert.GreaterOrEqual(t, instance.Status, 0)
        }
        
        fmt.Printf("实例总数: %d\n", resp.Total)
    })
    
    // 测试用例2: 按状态过滤实例
    t.Run("查询运行中的实例", func(t *testing.T) {
        req := &types.InstanceListRequest{
            PaginationRequest: types.PaginationRequest{
                Page:     1,
                PageSize: 10,
            },
            Status: 2, // 假设2表示运行中
        }
        resp, err := client.GetInstances(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        
        // 验证所有返回的实例都是运行状态
        for _, instance := range resp.Instance {
            assert.Equal(t, 2, instance.Status)
        }
    })
}
```

### 6. GetInstanceStatus() - 查询实例状态测试

**测试目标**: 验证实例状态查询功能

**测试用例**:

```go
func TestGetInstanceStatus(t *testing.T) {
    client := createTestClient()
    
    // 需要一个有效的实例UUID进行测试
    testInstanceUuid := "test-instance-uuid"
    
    // 测试用例1: 查询有效实例状态
    t.Run("查询有效实例状态", func(t *testing.T) {
        req := &types.InstanceStatusRequest{
            InstanceUuid: testInstanceUuid,
        }
        resp, err := client.GetInstanceStatus(req)
        
        if err != nil {
            // 如果实例不存在，这是预期的
            t.Logf("实例不存在或查询失败: %v", err)
            return
        }
        
        assert.NotNil(t, resp)
        assert.NotEmpty(t, resp.Instance.Uuid)
        assert.NotEmpty(t, resp.Instance.StatusTxt)
        
        fmt.Printf("实例状态: %s\n", resp.Instance.StatusTxt)
    })
    
    // 测试用例2: 测试必填参数验证
    t.Run("测试空UUID参数", func(t *testing.T) {
        req := &types.InstanceStatusRequest{
            InstanceUuid: "",
        }
        _, err := client.GetInstanceStatus(req)
        
        assert.Error(t, err)
        assert.Contains(t, err.Error(), "instance_uuid is required")
    })
}
```

## 资源查询API测试

### 7. GetGpuModels() - 查询GPU资源测试

**测试目标**: 验证GPU资源查询功能

**测试用例**:

```go
func TestGetGpuModels(t *testing.T) {
    client := createTestClient()
    
    // 测试用例1: 查询所有GPU资源
    t.Run("查询所有GPU资源", func(t *testing.T) {
        req := &types.GpuModelsRequest{}
        resp, err := client.GetGpuModels(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.GreaterOrEqual(t, resp.Total, 0)
        assert.NotNil(t, resp.NoCardPrice)
        
        // 验证GPU资源信息
        for _, gpu := range resp.Items {
            assert.NotEmpty(t, gpu.Uuid)
            assert.NotEmpty(t, gpu.Title)
            assert.NotEmpty(t, gpu.Price)
            assert.NotEmpty(t, gpu.FreeTxt)
        }
        
        fmt.Printf("GPU资源总数: %d\n", resp.Total)
        fmt.Printf("无卡价格: %s\n", resp.NoCardPrice.String())
    })
    
    // 测试用例2: 查询特定镜像的GPU资源
    t.Run("查询特定镜像GPU资源", func(t *testing.T) {
        // 首先获取一个镜像UUID
        podsResp, err := client.GetPods(&types.PodListRequest{
            PaginationRequest: types.PaginationRequest{Page: 1, PageSize: 1},
        })
        if err != nil || len(podsResp.Items) == 0 {
            t.Skip("没有可用的镜像进行测试")
            return
        }
        
        req := &types.GpuModelsRequest{
            PodUuid: podsResp.Items[0].Uuid,
        }
        resp, err := client.GetGpuModels(req)
        
        assert.NoError(t, err)
        assert.NotNil(t, resp)
    })
}
```

## 实例操作API测试

### 8. CreateInstance() - 创建实例测试

**测试目标**: 验证实例创建功能

**测试用例**:

```go
func TestCreateInstance(t *testing.T) {
    client := createTestClient()

    // 测试用例1: 创建实例（需要有效的镜像和GPU资源）
    t.Run("创建实例", func(t *testing.T) {
        // 首先获取可用的镜像
        podsResp, err := client.GetPods(&types.PodListRequest{
            PaginationRequest: types.PaginationRequest{Page: 1, PageSize: 1},
        })
        if err != nil || len(podsResp.Items) == 0 {
            t.Skip("没有可用的镜像")
            return
        }

        // 获取可用的GPU资源
        gpuResp, err := client.GetGpuModels(&types.GpuModelsRequest{
            PodUuid: podsResp.Items[0].Uuid,
        })
        if err != nil || len(gpuResp.Items) == 0 {
            t.Skip("没有可用的GPU资源")
            return
        }

        req := &types.InstanceCreateRequest{
            PodUuid:      podsResp.Items[0].Uuid,
            GpuModelUuid: gpuResp.Items[0].Uuid,
            AutoStart:    1,
        }
        resp, err := client.CreateInstance(req)

        assert.NoError(t, err)
        assert.NotNil(t, resp)
        assert.NotEmpty(t, resp.Instance.Uuid)
        assert.NotEmpty(t, resp.Instance.Title)

        fmt.Printf("实例创建成功: %s (%s)\n", resp.Instance.Title, resp.Instance.Uuid)

        // 清理：记录实例UUID以便后续清理
        // 注意：在实际测试中应该在测试结束后清理资源
    })

    // 测试用例2: 测试必填参数验证
    t.Run("测试缺少PodUuid", func(t *testing.T) {
        req := &types.InstanceCreateRequest{
            GpuModelUuid: "test-gpu-uuid",
            AutoStart:    1,
        }
        _, err := client.CreateInstance(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "pod_uuid is required")
    })

    t.Run("测试缺少GpuModelUuid", func(t *testing.T) {
        req := &types.InstanceCreateRequest{
            PodUuid:   "test-pod-uuid",
            AutoStart: 1,
        }
        _, err := client.CreateInstance(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "gpu_model_uuid is required")
    })
}
```

### 9. StartInstance() - 启动实例测试

**测试目标**: 验证实例启动功能

**测试用例**:

```go
func TestStartInstance(t *testing.T) {
    client := createTestClient()

    // 需要一个有效的实例UUID进行测试
    testInstanceUuid := "test-instance-uuid"

    // 测试用例1: 启动实例
    t.Run("启动实例", func(t *testing.T) {
        req := &types.InstanceActionRequest{
            InstanceUuid: testInstanceUuid,
        }
        err := client.StartInstance(req)

        if err != nil {
            // 如果实例不存在或已经在运行，记录但不失败
            t.Logf("启动实例失败（可能实例不存在或已运行）: %v", err)
            return
        }

        fmt.Printf("实例启动成功: %s\n", testInstanceUuid)
    })

    // 测试用例2: 测试空UUID参数
    t.Run("测试空UUID参数", func(t *testing.T) {
        req := &types.InstanceActionRequest{
            InstanceUuid: "",
        }
        err := client.StartInstance(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "instance_uuid is required")
    })
}
```

### 10. StopInstance() - 停止实例测试

**测试目标**: 验证实例停止功能

**测试用例**:

```go
func TestStopInstance(t *testing.T) {
    client := createTestClient()

    testInstanceUuid := "test-instance-uuid"

    // 测试用例1: 停止实例
    t.Run("停止实例", func(t *testing.T) {
        req := &types.InstanceActionRequest{
            InstanceUuid: testInstanceUuid,
        }
        err := client.StopInstance(req)

        if err != nil {
            t.Logf("停止实例失败（可能实例不存在或已停止）: %v", err)
            return
        }

        fmt.Printf("实例停止成功: %s\n", testInstanceUuid)
    })

    // 测试用例2: 测试参数验证
    t.Run("测试空UUID参数", func(t *testing.T) {
        req := &types.InstanceActionRequest{
            InstanceUuid: "",
        }
        err := client.StopInstance(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "instance_uuid is required")
    })
}
```

### 11. RestartInstance() - 重启实例测试

**测试目标**: 验证实例重启功能

**测试用例**:

```go
func TestRestartInstance(t *testing.T) {
    client := createTestClient()

    testInstanceUuid := "test-instance-uuid"

    // 测试用例1: 重启实例
    t.Run("重启实例", func(t *testing.T) {
        req := &types.InstanceActionRequest{
            InstanceUuid: testInstanceUuid,
        }
        err := client.RestartInstance(req)

        if err != nil {
            t.Logf("重启实例失败（可能实例不存在）: %v", err)
            return
        }

        fmt.Printf("实例重启成功: %s\n", testInstanceUuid)
    })

    // 测试用例2: 测试参数验证
    t.Run("测试空UUID参数", func(t *testing.T) {
        req := &types.InstanceActionRequest{
            InstanceUuid: "",
        }
        err := client.RestartInstance(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "instance_uuid is required")
    })
}
```

### 12. SetShutdownRegularTime() - 设置定时关机测试

**测试目标**: 验证定时关机设置功能

**测试用例**:

```go
func TestSetShutdownRegularTime(t *testing.T) {
    client := createTestClient()

    testInstanceUuid := "test-instance-uuid"

    // 测试用例1: 设置定时关机
    t.Run("设置定时关机", func(t *testing.T) {
        futureTime := time.Now().Add(24 * time.Hour).Format("2006-01-02 15:04:05")
        req := &types.InstanceShutdownRegularTimeRequest{
            InstanceUuid: testInstanceUuid,
            RegularTime:  futureTime,
            Cancel:       false,
        }
        err := client.SetShutdownRegularTime(req)

        if err != nil {
            t.Logf("设置定时关机失败（可能实例不存在）: %v", err)
            return
        }

        fmt.Printf("定时关机设置成功: %s at %s\n", testInstanceUuid, futureTime)
    })

    // 测试用例2: 取消定时关机
    t.Run("取消定时关机", func(t *testing.T) {
        req := &types.InstanceShutdownRegularTimeRequest{
            InstanceUuid: testInstanceUuid,
            RegularTime:  "2025-01-01 00:00:00",
            Cancel:       true,
        }
        err := client.SetShutdownRegularTime(req)

        if err != nil {
            t.Logf("取消定时关机失败（可能实例不存在）: %v", err)
            return
        }

        fmt.Printf("定时关机取消成功: %s\n", testInstanceUuid)
    })

    // 测试用例3: 测试参数验证
    t.Run("测试空UUID参数", func(t *testing.T) {
        req := &types.InstanceShutdownRegularTimeRequest{
            InstanceUuid: "",
            RegularTime:  "2025-01-01 00:00:00",
        }
        err := client.SetShutdownRegularTime(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "instance_uuid is required")
    })

    t.Run("测试空时间参数", func(t *testing.T) {
        req := &types.InstanceShutdownRegularTimeRequest{
            InstanceUuid: testInstanceUuid,
            RegularTime:  "",
        }
        err := client.SetShutdownRegularTime(req)

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "shutdown_time is required")
    })
}
```

## 错误处理测试

### 网络错误测试

**测试目标**: 验证网络错误的处理

```go
func TestNetworkErrors(t *testing.T) {
    // 使用无效的URL创建客户端
    client := client.NewClient("http://invalid-url-that-does-not-exist.com", "test-token")

    t.Run("网络连接失败", func(t *testing.T) {
        _, err := client.GetBalance(&types.BalanceRequest{})
        assert.Error(t, err)
        assert.Contains(t, err.Error(), "send request")
    })
}
```

### 认证错误测试

**测试目标**: 验证认证错误的处理

```go
func TestAuthenticationErrors(t *testing.T) {
    // 使用无效Token创建客户端
    client := client.NewClient(TestBaseURL, "invalid-token")

    t.Run("无效Token", func(t *testing.T) {
        _, err := client.GetBalance(&types.BalanceRequest{})
        if err != nil {
            // 验证是认证相关的错误
            assert.Contains(t, strings.ToLower(err.Error()), "auth")
        }
    })

    // 使用空Token创建客户端
    clientNoToken := client.NewClient(TestBaseURL, "")

    t.Run("空Token", func(t *testing.T) {
        _, err := clientNoToken.GetBalance(&types.BalanceRequest{})
        if err != nil {
            // 可能会返回认证错误
            t.Logf("空Token错误: %v", err)
        }
    })
}
```

### API错误响应测试

**测试目标**: 验证API错误响应的处理

```go
func TestAPIErrorResponses(t *testing.T) {
    client := createTestClient()

    t.Run("无效参数错误", func(t *testing.T) {
        // 使用无效的UUID测试
        _, err := client.GetInstanceStatus(&types.InstanceStatusRequest{
            InstanceUuid: "invalid-uuid-format",
        })
        if err != nil {
            t.Logf("无效UUID错误: %v", err)
            // 验证错误信息包含相关描述
            assert.NotEmpty(t, err.Error())
        }
    })

    t.Run("资源不存在错误", func(t *testing.T) {
        // 使用不存在的实例UUID
        _, err := client.GetInstanceStatus(&types.InstanceStatusRequest{
            InstanceUuid: "non-existent-instance-uuid",
        })
        if err != nil {
            t.Logf("资源不存在错误: %v", err)
        }
    })
}
```

## 完整测试套件

### 集成测试示例

**测试目标**: 验证完整的工作流程

```go
func TestCompleteWorkflow(t *testing.T) {
    client := createTestClient()

    t.Run("完整实例生命周期测试", func(t *testing.T) {
        // 1. 查询余额
        balanceResp, err := client.GetBalance(&types.BalanceRequest{})
        if err != nil {
            t.Fatalf("查询余额失败: %v", err)
        }
        t.Logf("当前余额: %s", balanceResp.Balance.String())

        // 2. 查询可用镜像
        podsResp, err := client.GetPods(&types.PodListRequest{
            PaginationRequest: types.PaginationRequest{Page: 1, PageSize: 5},
        })
        if err != nil {
            t.Fatalf("查询镜像失败: %v", err)
        }
        if len(podsResp.Items) == 0 {
            t.Skip("没有可用的镜像")
            return
        }
        selectedPod := podsResp.Items[0]
        t.Logf("选择镜像: %s", selectedPod.Title)

        // 3. 查询GPU资源
        gpuResp, err := client.GetGpuModels(&types.GpuModelsRequest{
            PodUuid: selectedPod.Uuid,
        })
        if err != nil {
            t.Fatalf("查询GPU资源失败: %v", err)
        }
        if len(gpuResp.Items) == 0 {
            t.Skip("没有可用的GPU资源")
            return
        }
        selectedGpu := gpuResp.Items[0]
        t.Logf("选择GPU: %s", selectedGpu.Title)

        // 4. 创建实例（可选，需要谨慎）
        // 注意：这会产生实际费用，在生产环境中应该跳过
        if testing.Short() {
            t.Skip("跳过实例创建测试（使用 -short 标志）")
            return
        }

        createResp, err := client.CreateInstance(&types.InstanceCreateRequest{
            PodUuid:      selectedPod.Uuid,
            GpuModelUuid: selectedGpu.Uuid,
            AutoStart:    0, // 不自动启动，避免产生费用
        })
        if err != nil {
            t.Logf("创建实例失败（可能是余额不足或资源不足）: %v", err)
            return
        }

        instanceUuid := createResp.Instance.Uuid
        t.Logf("实例创建成功: %s", instanceUuid)

        // 5. 查询实例状态
        statusResp, err := client.GetInstanceStatus(&types.InstanceStatusRequest{
            InstanceUuid: instanceUuid,
        })
        if err != nil {
            t.Logf("查询实例状态失败: %v", err)
        } else {
            t.Logf("实例状态: %s", statusResp.Instance.StatusTxt)
        }

        // 6. 清理：停止并删除实例
        // 注意：实际的删除操作可能需要额外的API调用
        err = client.StopInstance(&types.InstanceActionRequest{
            InstanceUuid: instanceUuid,
        })
        if err != nil {
            t.Logf("停止实例失败: %v", err)
        } else {
            t.Logf("实例已停止")
        }
    })
}
```

### 性能测试

**测试目标**: 验证API响应性能

```go
func TestPerformance(t *testing.T) {
    client := createTestClient()

    t.Run("并发查询测试", func(t *testing.T) {
        const concurrency = 10
        const requestsPerGoroutine = 5

        var wg sync.WaitGroup
        errors := make(chan error, concurrency*requestsPerGoroutine)

        start := time.Now()

        for i := 0; i < concurrency; i++ {
            wg.Add(1)
            go func() {
                defer wg.Done()
                for j := 0; j < requestsPerGoroutine; j++ {
                    _, err := client.GetBalance(&types.BalanceRequest{})
                    if err != nil {
                        errors <- err
                    }
                }
            }()
        }

        wg.Wait()
        close(errors)

        duration := time.Since(start)
        totalRequests := concurrency * requestsPerGoroutine

        // 检查错误
        errorCount := 0
        for err := range errors {
            errorCount++
            t.Logf("并发请求错误: %v", err)
        }

        t.Logf("并发测试完成: %d个请求，耗时%v，错误数量: %d",
            totalRequests, duration, errorCount)

        // 验证大部分请求成功
        successRate := float64(totalRequests-errorCount) / float64(totalRequests)
        assert.Greater(t, successRate, 0.8, "成功率应该大于80%")
    })

    t.Run("响应时间测试", func(t *testing.T) {
        const testCount = 10
        var totalDuration time.Duration

        for i := 0; i < testCount; i++ {
            start := time.Now()
            _, err := client.GetPods(&types.PodListRequest{
                PaginationRequest: types.PaginationRequest{Page: 1, PageSize: 10},
            })
            duration := time.Since(start)
            totalDuration += duration

            if err != nil {
                t.Logf("请求 %d 失败: %v", i+1, err)
            } else {
                t.Logf("请求 %d 耗时: %v", i+1, duration)
            }
        }

        avgDuration := totalDuration / testCount
        t.Logf("平均响应时间: %v", avgDuration)

        // 验证平均响应时间在合理范围内
        assert.Less(t, avgDuration, 10*time.Second, "平均响应时间应该小于10秒")
    })
}
```

## 测试运行指南

### 1. 创建测试文件

创建 `api_test.go` 文件，包含所有测试用例：

```go
package main

import (
    "strings"
    "sync"
    "testing"
    "time"

    "github.com/shopspring/decimal"
    "github.com/stretchr/testify/assert"
    "github.com/chenyu-ai/sdk-go/client"
    "github.com/chenyu-ai/sdk-go/types"
)

// 在这里添加上述所有测试函数
```

### 2. 运行测试命令

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestGetBalance

# 运行测试并生成覆盖率报告
go test -v -cover -coverprofile=coverage.out

# 查看覆盖率报告
go tool cover -html=coverage.out

# 运行短测试（跳过可能产生费用的测试）
go test -v -short

# 运行性能测试
go test -v -run TestPerformance

# 并行运行测试
go test -v -parallel 4
```

### 3. 测试配置

在运行测试前，请确保：

1. **配置正确的API地址和Token**
   ```go
   const (
       TestBaseURL = "https://api.chenyu.cn/api/v1"  // 替换为实际API地址
       TestToken   = "your-actual-token-here"        // 替换为实际Token
   )
   ```

2. **安装测试依赖**
   ```bash
   go get github.com/stretchr/testify/assert
   ```

3. **设置环境变量（可选）**
   ```bash
   export CHENYU_API_URL="https://api.chenyu.cn/api/v1"
   export CHENYU_API_TOKEN="your-token-here"
   ```

### 4. 测试最佳实践

1. **使用测试环境**: 确保使用测试环境的API地址，避免影响生产数据
2. **资源清理**: 测试创建的实例应该及时清理，避免产生不必要的费用
3. **错误处理**: 测试应该能够优雅地处理各种错误情况
4. **并发安全**: 验证SDK在并发使用时的安全性
5. **性能监控**: 定期运行性能测试，确保API响应时间在合理范围内

### 5. 持续集成配置

在CI/CD流水线中集成测试：

```yaml
# .github/workflows/test.yml
name: API Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-go@v2
      with:
        go-version: 1.19

    - name: Install dependencies
      run: go mod download

    - name: Run tests
      run: go test -v -short -cover
      env:
        CHENYU_API_URL: ${{ secrets.CHENYU_API_URL }}
        CHENYU_API_TOKEN: ${{ secrets.CHENYU_API_TOKEN }}
```

## 测试结果示例

### 成功测试输出示例

```
=== RUN   TestGetBalance
=== RUN   TestGetBalance/正常查询余额
账户余额: 100.50
算力卡数量: 2
--- PASS: TestGetBalance (0.45s)
    --- PASS: TestGetBalance/正常查询余额 (0.45s)

=== RUN   TestGetPods
=== RUN   TestGetPods/查询所有镜像
镜像总数: 25
=== RUN   TestGetPods/搜索Jupyter镜像
--- PASS: TestGetPods (0.32s)
    --- PASS: TestGetPods/查询所有镜像 (0.18s)
    --- PASS: TestGetPods/搜索Jupyter镜像 (0.14s)

=== RUN   TestGetGpuModels
=== RUN   TestGetGpuModels/查询所有GPU资源
GPU资源总数: 8
无卡价格: 0.50
--- PASS: TestGetGpuModels (0.28s)
    --- PASS: TestGetGpuModels/查询所有GPU资源 (0.28s)

PASS
coverage: 85.2% of statements
ok      github.com/chenyu-ai/sdk-go     2.156s
```

## 总结

本测试文档提供了晨羽智云 Go SDK 的全面测试方案，包括：

### 测试覆盖范围
- ✅ **财务API**: 余额查询、充值记录、账单查询
- ✅ **应用管理API**: 镜像列表、实例管理、状态查询
- ✅ **实例操作API**: 启动、停止、重启、定时关机
- ✅ **资源查询API**: GPU资源查询
- ✅ **错误处理**: 网络错误、认证错误、API错误
- ✅ **性能测试**: 并发测试、响应时间测试
- ✅ **集成测试**: 完整工作流程测试

### 测试特点
- **全面性**: 覆盖所有公开API接口
- **实用性**: 提供真实的使用场景测试
- **安全性**: 包含参数验证和错误处理测试
- **性能**: 验证API响应性能和并发安全性
- **可维护性**: 结构清晰，易于扩展和维护

### 使用建议
1. 在开发过程中定期运行测试，确保代码质量
2. 在发布前运行完整测试套件，验证所有功能正常
3. 使用短测试模式避免产生不必要的费用
4. 根据实际需求调整测试参数和配置
5. 持续监控测试结果，及时发现和解决问题

通过这套完整的测试方案，可以确保晨羽智云 Go SDK 的稳定性、可靠性和性能表现。

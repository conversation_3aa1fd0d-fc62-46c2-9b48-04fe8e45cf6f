package client

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/chenyu-ai/sdk-go/types"
)

func TestNewClient(t *testing.T) {
	client := NewClient("https://api.example.com", "test-token")
	
	if client.BaseURL != "https://api.example.com" {
		t.Errorf("Expected BaseURL to be 'https://api.example.com', got '%s'", client.BaseURL)
	}
	
	if client.Token != "test-token" {
		t.<PERSON><PERSON><PERSON>("Expected Token to be 'test-token', got '%s'", client.Token)
	}
	
	if client.HTTPClient.Timeout != 30*time.Second {
		t.Errorf("Expected default timeout to be 30s, got %v", client.HTTPClient.Timeout)
	}
}

func TestSetTimeout(t *testing.T) {
	client := NewClient("https://api.example.com", "test-token")
	client.SetTimeout(60 * time.Second)
	
	if client.HTTPClient.Timeout != 60*time.Second {
		t.<PERSON>rrorf("Expected timeout to be 60s, got %v", client.HTTPClient.Timeout)
	}
}

func TestGetBalance(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查请求方法
		if r.Method != "POST" {
			t.Errorf("Expected POST request, got %s", r.Method)
		}
		
		// 检查请求路径
		if r.URL.Path != "/finances/balance" {
			t.Errorf("Expected path '/finances/balance', got '%s'", r.URL.Path)
		}
		
		// 检查认证头
		auth := r.Header.Get("Authorization")
		if auth != "Bearer test-token" {
			t.Errorf("Expected Authorization header 'Bearer test-token', got '%s'", auth)
		}
		
		// 返回模拟响应
		response := map[string]interface{}{
			"code": 0,
			"msg":  "success",
			"result": map[string]interface{}{
				"items": []map[string]interface{}{
					{
						"id":       1,
						"uuid":     "card-uuid-1",
						"card_no":  "CARD001",
						"balance":  "100.50",
						"status":   1,
						"status_txt": "正常",
					},
				},
				"total": 1,
			},
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	// 创建客户端
	client := NewClient(server.URL, "test-token")
	
	// 调用API
	resp, err := client.GetBalance(&types.BalanceRequest{})
	if err != nil {
		t.Fatalf("GetBalance failed: %v", err)
	}
	
	// 验证响应
	if resp.Total != 1 {
		t.Errorf("Expected total to be 1, got %d", resp.Total)
	}
	
	if len(resp.Items) != 1 {
		t.Errorf("Expected 1 item, got %d", len(resp.Items))
	}
	
	if resp.Items[0].CardNo != "CARD001" {
		t.Errorf("Expected card_no to be 'CARD001', got '%s'", resp.Items[0].CardNo)
	}
}

func TestGetPodsWithPagination(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 解析请求体
		var req types.PodListRequest
		json.NewDecoder(r.Body).Decode(&req)
		
		// 验证分页参数
		if req.Page != 2 {
			t.Errorf("Expected page to be 2, got %d", req.Page)
		}
		if req.PageSize != 5 {
			t.Errorf("Expected page_size to be 5, got %d", req.PageSize)
		}
		
		// 返回模拟响应
		response := map[string]interface{}{
			"code": 0,
			"msg":  "success",
			"result": map[string]interface{}{
				"items": []map[string]interface{}{
					{
						"id":         1,
						"uuid":       "pod-uuid-1",
						"title":      "Jupyter Notebook",
						"status":     1,
						"status_txt": "正常",
					},
				},
				"total": 10,
			},
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	// 创建客户端
	client := NewClient(server.URL, "test-token")
	
	// 调用API
	resp, err := client.GetPods(&types.PodListRequest{
		PaginationRequest: types.PaginationRequest{
			Page:     2,
			PageSize: 5,
		},
	})
	if err != nil {
		t.Fatalf("GetPods failed: %v", err)
	}
	
	// 验证响应
	if resp.Total != 10 {
		t.Errorf("Expected total to be 10, got %d", resp.Total)
	}
}

func TestAPIErrorHandling(t *testing.T) {
	// 创建模拟服务器返回API错误
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"code": 1,
			"msg":  "参数错误",
			"result": nil,
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	// 创建客户端
	client := NewClient(server.URL, "test-token")
	
	// 调用API
	_, err := client.GetBalance(&types.BalanceRequest{})
	if err == nil {
		t.Fatal("Expected error, got nil")
	}
	
	// 验证错误信息
	expectedError := "API error (code 1): 参数错误"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

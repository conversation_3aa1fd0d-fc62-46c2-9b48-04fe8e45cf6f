package main

import (
	"fmt"
	"log"
	"strings"
	"testing"
	"time"

	"github.com/chenyu-ai/sdk-go/client"
	"github.com/chenyu-ai/sdk-go/types"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// 测试配置
const (
	TestBaseURL = "https://www.chenyu.cn/api/v1"                  // 测试环境URL
	TestToken   = "sk-kyhHuxOoAry36cgX1mZTWSWEAet16GsVtz5HNzODql" // 测试Token
)

// 创建测试客户端
func createTestClient() *client.Client {
	return client.NewClient(TestBaseURL, TestToken)
}

// TestGetBalance 测试余额查询
func TestGetBalance(t *testing.T) {
	client := createTestClient()

	t.Run("正常查询余额", func(t *testing.T) {
		req := &types.BalanceRequest{}
		resp, err := client.GetBalance(req)

		if err != nil {
			t.Logf("查询余额失败: %v", err)
			return
		}

		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Balance)
		assert.NotNil(t, resp.Cards)

		// 验证算力卡信息
		for _, card := range resp.Cards {
			assert.NotEmpty(t, card.Uuid)
			assert.NotEmpty(t, card.CardNo)
			assert.NotEmpty(t, card.StatusTxt)
			assert.True(t, card.LeaveAmount.GreaterThanOrEqual(decimal.Zero))
		}

		fmt.Printf("账户余额: %s\n", resp.Balance.String())
		fmt.Printf("算力卡数量: %d\n", len(resp.Cards))

		// 详细显示算力卡信息（从examples迁移）
		for _, card := range resp.Cards {
			fmt.Printf("卡号: %s, 剩余金额: %s, 状态: %s\n",
				card.CardNo, card.LeaveAmount.String(), card.StatusTxt)
		}
	})
}

// TestGetPods 测试镜像列表查询
func TestGetPods(t *testing.T) {
	client := createTestClient()

	t.Run("查询所有镜像", func(t *testing.T) {
		req := &types.PodListRequest{
			PaginationRequest: types.PaginationRequest{
				Page:     1,
				PageSize: 10,
			},
		}
		resp, err := client.GetPods(req)

		if err != nil {
			t.Logf("查询镜像失败: %v", err)
			return
		}

		assert.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, 0)

		// 验证镜像信息
		for _, pod := range resp.Items {
			assert.NotEmpty(t, pod.Uuid)
			assert.NotEmpty(t, pod.Title)
			assert.NotEmpty(t, pod.StatusTxt)
		}

		fmt.Printf("镜像总数: %d\n", resp.Total)
	})

	t.Run("搜索Jupyter镜像", func(t *testing.T) {
		req := &types.PodListRequest{
			PaginationRequest: types.PaginationRequest{
				Page:     1,
				PageSize: 5,
			},
			Kw: "jupyter",
		}
		resp, err := client.GetPods(req)

		if err != nil {
			t.Logf("搜索镜像失败: %v", err)
			return
		}

		assert.NotNil(t, resp)

		// 验证搜索结果包含关键词
		for _, pod := range resp.Items {
			assert.Contains(t, strings.ToLower(pod.Title), "jupyter")
		}

		fmt.Printf("找到 %d 个Jupyter镜像\n", len(resp.Items))
	})
}

// TestGetGpuModels 测试GPU资源查询
func TestGetGpuModels(t *testing.T) {
	client := createTestClient()

	t.Run("查询所有GPU资源", func(t *testing.T) {
		req := &types.GpuModelsRequest{}
		resp, err := client.GetGpuModels(req)

		if err != nil {
			t.Logf("查询GPU资源失败: %v", err)
			return
		}

		assert.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, 0)
		assert.NotNil(t, resp.NoCardPrice)

		// 验证GPU资源信息
		for _, gpu := range resp.Items {
			assert.NotEmpty(t, gpu.Uuid)
			assert.NotEmpty(t, gpu.Title)
			assert.NotEmpty(t, gpu.Price)
			assert.NotEmpty(t, gpu.FreeTxt)
		}

		fmt.Printf("GPU资源总数: %d\n", resp.Total)
		fmt.Printf("无卡价格: %s\n", resp.NoCardPrice.String())
	})
}

// TestGetInstances 测试实例列表查询
func TestGetInstances(t *testing.T) {
	client := createTestClient()

	t.Run("查询所有实例", func(t *testing.T) {
		req := &types.InstanceListRequest{
			PaginationRequest: types.PaginationRequest{
				Page:     1,
				PageSize: 10,
			},
		}
		resp, err := client.GetInstances(req)

		if err != nil {
			t.Logf("查询实例失败: %v", err)
			return
		}

		assert.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, 0)

		// 验证实例信息
		for _, instance := range resp.Instance {
			assert.NotEmpty(t, instance.Uuid)
			assert.NotEmpty(t, instance.Title)
			assert.NotEmpty(t, instance.StatusTxt)
			assert.GreaterOrEqual(t, instance.Status, 0)
		}

		fmt.Printf("实例总数: %d\n", resp.Total)
	})
}

// TestGetBill 测试账单查询
func TestGetBill(t *testing.T) {
	client := createTestClient()

	t.Run("查询账单列表", func(t *testing.T) {
		req := &types.BillListRequest{
			PaginationRequest: types.PaginationRequest{
				Page:     1,
				PageSize: 10,
			},
		}
		resp, err := client.GetBill(req)

		if err != nil {
			t.Logf("查询账单失败: %v", err)
			return
		}

		assert.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, 0)

		// 验证账单记录字段
		for _, bill := range resp.Items {
			assert.NotEmpty(t, bill.OrderNo)
			assert.NotEmpty(t, bill.Show)
			assert.NotNil(t, bill.OccurredAmount)
		}

		fmt.Printf("账单总数: %d\n", resp.Total)
	})
}

// TestInstanceOperations 测试实例操作
func TestInstanceOperations(t *testing.T) {
	client := createTestClient()

	// 使用一个测试实例UUID（需要替换为实际存在的实例）
	testInstanceUuid := "c1ce8c227f1946918dd87c9d4a7632d0"

	t.Run("查询实例状态", func(t *testing.T) {
		req := &types.InstanceStatusRequest{
			InstanceUuid: testInstanceUuid,
		}
		resp, err := client.GetInstanceStatus(req)

		if err != nil {
			t.Logf("查询实例状态失败: %v", err)
			return
		}

		assert.NotNil(t, resp)
		assert.NotEmpty(t, resp.Instance.Uuid)
		assert.NotEmpty(t, resp.Instance.StatusTxt)

		fmt.Printf("实例状态: %s\n", resp.Instance.StatusTxt)
	})

	t.Run("测试参数验证", func(t *testing.T) {
		// 测试空UUID参数
		req := &types.InstanceStatusRequest{
			InstanceUuid: "",
		}
		_, err := client.GetInstanceStatus(req)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "instance_uuid is required")
	})
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	t.Run("无效Token", func(t *testing.T) {
		// 使用无效Token创建客户端
		invalidClient := client.NewClient(TestBaseURL, "invalid-token")

		_, err := invalidClient.GetBalance(&types.BalanceRequest{})
		if err != nil {
			t.Logf("无效Token错误: %v", err)
			// 验证错误信息不为空
			assert.NotEmpty(t, err.Error())
		}
	})

	t.Run("网络错误", func(t *testing.T) {
		// 使用无效URL创建客户端
		networkClient := client.NewClient("http://invalid-url.com", "test-token")

		_, err := networkClient.GetBalance(&types.BalanceRequest{})
		assert.Error(t, err)
		t.Logf("网络错误: %v", err)
	})
}

// TestClientConfiguration 测试客户端配置
func TestClientConfiguration(t *testing.T) {
	t.Run("创建客户端", func(t *testing.T) {
		client := client.NewClient("https://api.example.com", "test-token")

		assert.Equal(t, "https://api.example.com", client.BaseURL)
		assert.Equal(t, "test-token", client.Token)
		assert.Equal(t, 30*time.Second, client.HTTPClient.Timeout)
	})

	t.Run("设置超时时间", func(t *testing.T) {
		client := client.NewClient("https://api.example.com", "test-token")
		client.SetTimeout(60 * time.Second)

		assert.Equal(t, 60*time.Second, client.HTTPClient.Timeout)
	})
}

// 运行所有测试的主函数
func main() {
	fmt.Println("运行晨羽智云 Go SDK API 测试...")
	fmt.Println("请确保已配置正确的API地址和Token")
	fmt.Println()

	// 这里可以添加一些基本的连接测试
	client := createTestClient()
	_, err := client.GetBalance(&types.BalanceRequest{})
	if err != nil {
		log.Printf("连接测试失败: %v", err)
		log.Println("请检查API地址和Token配置")
	} else {
		log.Println("连接测试成功")
	}
}

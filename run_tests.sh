#!/bin/bash

# 晨羽智云 Go SDK API 测试运行脚本

echo "=========================================="
echo "晨羽智云 Go SDK API 测试套件"
echo "=========================================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

echo "Go版本: $(go version)"
echo

# 检查依赖
echo "检查依赖..."
if ! go list -m github.com/stretchr/testify &> /dev/null; then
    echo "安装测试依赖..."
    go get github.com/stretchr/testify/assert
fi

if ! go list -m github.com/shopspring/decimal &> /dev/null; then
    echo "安装decimal依赖..."
    go get github.com/shopspring/decimal
fi

echo "依赖检查完成"
echo

# 设置测试环境变量（可选）
export CHENYU_API_URL="${CHENYU_API_URL:-https://api.chenyu.cn/api/v1}"
export CHENYU_API_TOKEN="${CHENYU_API_TOKEN:-your-token-here}"

echo "测试配置:"
echo "API URL: $CHENYU_API_URL"
echo "Token: ${CHENYU_API_TOKEN:0:10}..."
echo

# 运行测试选项
case "${1:-all}" in
    "basic")
        echo "运行基础API测试..."
        go test -v -run "TestGetBalance|TestGetPods|TestGetGpuModels" ./...
        ;;
    "financial")
        echo "运行财务相关API测试..."
        go test -v -run "TestGetBalance|TestGetBill|TestGetRecharge" ./...
        ;;
    "instance")
        echo "运行实例相关API测试..."
        go test -v -run "TestGetInstances|TestInstanceOperations" ./...
        ;;
    "error")
        echo "运行错误处理测试..."
        go test -v -run "TestErrorHandling" ./...
        ;;
    "short")
        echo "运行短测试（跳过可能产生费用的测试）..."
        go test -v -short ./...
        ;;
    "coverage")
        echo "运行测试并生成覆盖率报告..."
        go test -v -cover -coverprofile=coverage.out ./...
        if [ -f coverage.out ]; then
            echo "生成HTML覆盖率报告..."
            go tool cover -html=coverage.out -o coverage.html
            echo "覆盖率报告已生成: coverage.html"
        fi
        ;;
    "performance")
        echo "运行性能测试..."
        go test -v -run "TestPerformance" ./...
        ;;
    "all")
        echo "运行所有测试..."
        go test -v ./...
        ;;
    "help")
        echo "使用方法: $0 [选项]"
        echo
        echo "选项:"
        echo "  basic      - 运行基础API测试"
        echo "  financial  - 运行财务相关API测试"
        echo "  instance   - 运行实例相关API测试"
        echo "  error      - 运行错误处理测试"
        echo "  short      - 运行短测试（跳过费用测试）"
        echo "  coverage   - 运行测试并生成覆盖率报告"
        echo "  performance- 运行性能测试"
        echo "  all        - 运行所有测试（默认）"
        echo "  help       - 显示此帮助信息"
        echo
        echo "环境变量:"
        echo "  CHENYU_API_URL   - API地址（默认: https://api.chenyu.cn/api/v1）"
        echo "  CHENYU_API_TOKEN - API Token"
        echo
        echo "示例:"
        echo "  $0 basic                    # 运行基础测试"
        echo "  $0 short                    # 运行短测试"
        echo "  CHENYU_API_TOKEN=xxx $0 all # 使用指定Token运行所有测试"
        exit 0
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 '$0 help' 查看帮助信息"
        exit 1
        ;;
esac

echo
echo "测试完成!"

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "✅ 所有测试通过"
else
    echo "❌ 部分测试失败"
    exit 1
fi

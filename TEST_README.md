# 晨羽智云 Go SDK 测试指南

本目录包含了晨羽智云 Go SDK 的完整测试文档和示例代码。

## 文件说明

- `API_TEST_DOCUMENTATION.md` - 完整的API测试文档，包含所有接口的详细测试用例
- `api_test_example.go` - 可运行的测试示例代码
- `run_tests.sh` - 测试运行脚本（Linux/macOS）
- `TEST_README.md` - 本说明文件

## 快速开始

### 1. 环境准备

确保您已经安装了Go环境（推荐Go 1.19+）：

```bash
go version
```

### 2. 安装依赖

```bash
# 安装测试依赖
go get github.com/stretchr/testify/assert
go get github.com/shopspring/decimal

# 确保SDK依赖已安装
go mod tidy
```

### 3. 配置测试环境

在运行测试前，请修改 `api_test_example.go` 中的配置：

```go
const (
    TestBaseURL = "https://api.chenyu.cn/api/v1"  // 替换为您的API地址
    TestToken   = "your-actual-token-here"        // 替换为您的实际Token
)
```

或者设置环境变量：

```bash
export CHENYU_API_URL="https://api.chenyu.cn/api/v1"
export CHENYU_API_TOKEN="your-token-here"
```

### 4. 运行测试

#### 方式一：使用Go命令直接运行

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestGetBalance

# 运行短测试（跳过可能产生费用的测试）
go test -v -short

# 生成覆盖率报告
go test -v -cover -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

#### 方式二：使用测试脚本（Linux/macOS）

```bash
# 给脚本执行权限
chmod +x run_tests.sh

# 运行基础测试
./run_tests.sh basic

# 运行所有测试
./run_tests.sh all

# 查看帮助
./run_tests.sh help
```

#### 方式三：在Windows上运行

```powershell
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestGetBalance

# 生成覆盖率报告
go test -v -cover -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

## 测试分类

### 基础API测试
- `TestGetBalance` - 余额查询测试
- `TestGetPods` - 镜像列表查询测试
- `TestGetGpuModels` - GPU资源查询测试
- `TestGetInstances` - 实例列表查询测试

### 财务相关测试
- `TestGetBalance` - 余额查询
- `TestGetBill` - 账单查询
- `TestGetRecharge` - 充值记录查询

### 实例操作测试
- `TestInstanceOperations` - 实例状态查询和操作
- `TestCreateInstance` - 实例创建（谨慎使用）
- `TestStartInstance` - 实例启动
- `TestStopInstance` - 实例停止

### 错误处理测试
- `TestErrorHandling` - 网络错误、认证错误等
- `TestClientConfiguration` - 客户端配置测试

## 注意事项

### ⚠️ 重要提醒

1. **费用风险**: 某些测试（如创建实例）可能会产生实际费用，请谨慎运行
2. **测试环境**: 建议使用测试环境的API地址，避免影响生产数据
3. **Token安全**: 不要将真实的Token提交到版本控制系统
4. **资源清理**: 测试创建的资源应及时清理

### 🔒 安全建议

1. 使用专门的测试Token，限制权限范围
2. 定期轮换测试Token
3. 在CI/CD中使用环境变量存储敏感信息
4. 不要在公共代码库中硬编码Token

### 📊 测试最佳实践

1. **分层测试**: 从单元测试到集成测试逐步验证
2. **错误处理**: 确保所有错误情况都有相应的测试
3. **性能测试**: 定期运行性能测试，监控API响应时间
4. **并发测试**: 验证SDK在并发环境下的稳定性

## 测试结果解读

### 成功示例
```
=== RUN   TestGetBalance
账户余额: 100.50
算力卡数量: 2
--- PASS: TestGetBalance (0.45s)

PASS
coverage: 85.2% of statements
ok      github.com/chenyu-ai/sdk-go     2.156s
```

### 失败处理
如果测试失败，请检查：
1. API地址和Token是否正确
2. 网络连接是否正常
3. API服务是否可用
4. 账户余额是否充足（对于需要费用的操作）

## 持续集成

### GitHub Actions 示例

```yaml
name: API Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: Install dependencies
      run: go mod download
    
    - name: Run tests
      run: go test -v -short -cover
      env:
        CHENYU_API_URL: ${{ secrets.CHENYU_API_URL }}
        CHENYU_API_TOKEN: ${{ secrets.CHENYU_API_TOKEN }}
```

## 扩展测试

如果需要添加新的测试用例：

1. 在 `api_test_example.go` 中添加新的测试函数
2. 遵循 `TestXxx` 命名规范
3. 使用 `t.Run()` 创建子测试
4. 添加适当的断言和错误处理
5. 更新文档说明

## 支持

如果在测试过程中遇到问题：

1. 查看 `API_TEST_DOCUMENTATION.md` 获取详细的测试说明
2. 检查API文档确认接口规范
3. 联系技术支持获取帮助

## 版本兼容性

- Go 1.19+
- 晨羽智云 Go SDK v1.0.0+
- testify v1.8.0+

---

**最后更新**: 2024年1月
**维护者**: 晨羽智云技术团队
